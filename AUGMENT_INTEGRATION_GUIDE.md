# 🚀 Search Log MCP 集成到 Augment 指南

## 📋 概述

这个MCP工具提供了强大的日志查询和分析功能，包括：
- 🕐 时间戳转换工具
- 🔍 智能SQL构建器
- 📊 日志搜索和分析

## ✅ 已完成的修复

1. **Python版本升级** - 从3.9.6升级到3.10.12
2. **MCP依赖安装** - 成功安装mcp[cli]包
3. **代码质量提升** - 完整的类型注解、错误处理、参数验证
4. **安全性增强** - SQL注入防护、输入验证
5. **测试验证** - 完整的功能测试通过

## 🛠️ 可用工具

### 1. get_time_stamp
- **功能**: 时间戳转换
- **参数**: `time` (可选) - 格式: "YYYY-MM-DD HH:MM:SS"
- **返回**: 秒级时间戳

### 2. build_sql
- **功能**: 智能SQL构建
- **参数**:
  - `traceId` (可选) - 追踪ID
  - `start_time` (可选) - 开始时间戳
  - `end_time` (可选) - 结束时间戳
  - `level` (可选) - 日志级别 (INFO/WARN/ERROR/DEBUG)
  - `table_name` (可选) - 表名
  - `objectId` (可选) - 对象ID
  - `msg` (可选) - 消息内容
- **返回**: 构建好的SQL查询语句

### 3. search_app_log
- **功能**: 执行日志查询
- **参数**: `sql` - SQL查询语句
- **返回**: 格式化的日志结果

## 🔧 集成到Augment

### 方法1: 直接配置文件集成

将以下配置添加到你的Augment MCP配置中：

```json
{
  "mcpServers": {
    "search-log": {
      "command": "python",
      "args": ["/Users/<USER>/PycharmProjects/searchLog/seach_log_mcp/search_log.py"],
      "env": {
        "PYTHONPATH": "/Users/<USER>/PycharmProjects/searchLog",
        "PATH": "/Users/<USER>/PycharmProjects/searchLog/venv/bin:/usr/local/bin:/usr/bin:/bin"
      }
    }
  }
}
```

### 方法2: 使用启动脚本

创建启动脚本以确保环境正确：

```bash
#!/bin/bash
cd /Users/<USER>/PycharmProjects/searchLog
source venv/bin/activate
python seach_log_mcp/search_log.py
```

## 🧪 测试验证

运行测试客户端验证功能：
```bash
python test_mcp_client.py
```

## 📝 使用示例

在Augment中，你可以这样使用：

1. **获取当前时间戳**:
   ```
   请帮我获取当前时间戳
   ```

2. **构建查询SQL**:
   ```
   请帮我构建一个查询SQL，查找traceId为"abc-123"的ERROR级别日志
   ```

3. **查询日志**:
   ```
   请帮我查询最近1小时的错误日志
   ```

## 🔍 故障排除

如果遇到问题：

1. **检查Python版本**: 确保使用Python 3.10+
2. **检查虚拟环境**: 确保激活了正确的venv
3. **检查依赖**: 确保安装了mcp[cli]
4. **检查路径**: 确保配置中的路径正确

## 🎯 下一步

1. 在Augment中配置MCP服务器
2. 重启Augment
3. 测试日志查询功能
4. 根据需要调整配置

#!/bin/bash

# Search Log MCP Server 启动脚本
# 确保在正确的环境中启动MCP服务器

# 设置项目目录
PROJECT_DIR="/Users/<USER>/PycharmProjects/searchLog"

# 切换到项目目录
cd "$PROJECT_DIR" || {
    echo "❌ 无法切换到项目目录: $PROJECT_DIR"
    exit 1
}

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先创建虚拟环境"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate || {
    echo "❌ 无法激活虚拟环境"
    exit 1
}

# 检查Python版本
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
echo "🐍 使用Python版本: $PYTHON_VERSION"

# 检查MCP是否安装
if ! python -c "import mcp" 2>/dev/null; then
    echo "❌ MCP包未安装，请运行: pip install 'mcp[cli]'"
    exit 1
fi

echo "✅ 环境检查通过"
echo "🚀 启动Search Log MCP服务器..."

# 启动MCP服务器
exec python seach_log_mcp/search_log.py

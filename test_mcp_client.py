#!/usr/bin/env python3
"""
测试MCP客户端 - 验证search_log MCP服务器
"""

import asyncio
import os
import sys
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 配置服务器参数
server_params = StdioServerParameters(
    command="python",
    args=[os.path.join(os.path.dirname(__file__), "seach_log_mcp", "search_log.py")],
    env={
        "PYTHONPATH": os.path.dirname(__file__),
        "PATH": os.path.join(os.path.dirname(__file__), "venv", "bin") + ":" + os.environ.get("PATH", "")
    }
)

async def test_mcp_server():
    """测试MCP服务器功能"""
    print("🚀 开始测试MCP服务器...")
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                await session.initialize()
                print("✅ MCP服务器连接成功！")
                
                # 列出可用工具
                tools = await session.list_tools()
                print(f"\n🛠️ 可用工具 ({len(tools.tools)} 个):")
                for tool in tools.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试 get_time_stamp 工具
                print("\n🧪 测试 get_time_stamp 工具:")
                result = await session.call_tool("get_time_stamp", {})
                if result.content:
                    print(f"  当前时间戳: {result.content[0].text}")
                
                # 测试带参数的 get_time_stamp
                result = await session.call_tool("get_time_stamp", {
                    "time": "2025-07-25 12:00:00"
                })
                if result.content:
                    print(f"  指定时间戳: {result.content[0].text}")
                
                # 测试 build_sql 工具
                print("\n🧪 测试 build_sql 工具:")
                result = await session.call_tool("build_sql", {
                    "traceId": "test-trace-123",
                    "level": "ERROR",
                    "start_time": 1721900000,
                    "end_time": 1721903600
                })
                if result.content:
                    print(f"  生成的SQL: {result.content[0].text}")
                
                print("\n✅ 所有测试通过！MCP服务器工作正常。")
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    
    return True

async def main():
    """主函数"""
    success = await test_mcp_server()
    
    if success:
        print("\n🎉 MCP服务器已准备好集成到Augment！")
        print("\n📋 集成步骤:")
        print("1. 将 mcp_config.json 添加到你的Augment配置中")
        print("2. 重启Augment以加载新的MCP服务器")
        print("3. 在Augment中测试日志查询功能")
    else:
        print("\n❌ 请检查配置并重试")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
